use anyhow::Result;
use chrono::{Duration, NaiveDate};
use futures::stream::{self, StreamExt};
use polars::prelude::*;
use reqwest::Client;
use std::fs::{self, File};
use std::io::Read;
use std::path::{Path, PathBuf};
use std::sync::Arc;

const BASE_URL: &str = "https://data.binance.vision/data/spot/daily/aggTrades";
const MAX_CONCURRENT_REQUESTS: usize = 20; // Number of parallel downloads
const BATCH_SIZE: usize = 30; // Save data every 30 days to avoid losing progress

/// Check if CSV file exists and get the latest date from it
async fn get_latest_date_from_csv(csv_path: &Path) -> Result<Option<NaiveDate>> {
    if !csv_path.exists() {
        return Ok(None);
    }

    // Use spawn_blocking to avoid runtime conflicts
    let csv_path = csv_path.to_path_buf();
    let result = tokio::task::spawn_blocking(move || -> Result<Option<NaiveDate>> {
        let df = LazyCsvReader::new(&csv_path)
            .finish()?
            .select([col("timestamp")])
            .collect()?;

        if df.height() == 0 {
            return Ok(None);
        }

        // Get the maximum timestamp and convert to date
        let max_timestamp = df
            .lazy()
            .select([col("timestamp").max()])
            .collect()?
            .column("timestamp")?
            .get(0)?
            .try_extract::<i64>()?;

        // Convert microseconds to date (Binance data from 2025 onwards is in microseconds)
        let timestamp_ms = if max_timestamp > 1_000_000_000_000_000 {
            max_timestamp / 1000 // Convert microseconds to milliseconds
        } else {
            max_timestamp // Already in milliseconds
        };

        let datetime = chrono::DateTime::from_timestamp_millis(timestamp_ms)
            .ok_or_else(|| anyhow::anyhow!("Invalid timestamp: {}", timestamp_ms))?;

        Ok(Some(datetime.date_naive()))
    }).await??;

    Ok(result)
}

/// Main entry point to download all aggregate trades for a given crypto pair.
async fn download_agg_trades_for_pair(
    pair: &str,
    start_date: NaiveDate,
    output_dir: &Path,
) -> Result<()> {
    let end_date = chrono::Utc::now().date_naive(); // Download up to today
    let client = Arc::new(Client::new());
    fs::create_dir_all(output_dir)?;

    // Define the single CSV file path
    let csv_path = output_dir.join(format!("{}_agg_trades.csv", pair));

    // Check if we already have data and determine the start date
    let actual_start_date = match get_latest_date_from_csv(&csv_path).await? {
        Some(latest_date) => {
            println!("Found existing data up to {}. Resuming from {}",
                     latest_date, latest_date + Duration::days(1));
            latest_date + Duration::days(1)
        }
        None => {
            println!("No existing data found. Starting from {}", start_date);
            start_date
        }
    };

    if actual_start_date > end_date {
        println!("Data is already up to date for {}", pair);
        return Ok(());
    }

    println!(
        "Starting download for {} from {} to {}",
        pair, actual_start_date, end_date
    );

    // Create a stream of dates to process
    let dates: Vec<NaiveDate> = actual_start_date
        .iter_days()
        .take_while(|&d| d <= end_date)
        .collect();

    println!("Total dates to process: {}", dates.len());

    // Process dates in batches to save progress incrementally
    let mut total_saved = 0;
    for (batch_num, date_batch) in dates.chunks(BATCH_SIZE).enumerate() {
        println!("Processing batch {} ({} dates)...", batch_num + 1, date_batch.len());

        // Process the downloads concurrently for this batch
        let results: Vec<_> = stream::iter(date_batch.iter().cloned())
            .map(|date| {
                let client = Arc::clone(&client);
                async move {
                    download_day_data(client, pair, date).await
                }
            })
            .buffer_unordered(MAX_CONCURRENT_REQUESTS)
            .collect()
            .await;

        // Filter successful results for this batch
        let successful_data: Vec<_> = results
            .into_iter()
            .filter_map(|result| match result {
                Ok(Some(data)) => Some(data),
                Ok(None) => None, // No data for this day
                Err(e) => {
                    eprintln!("Error downloading data: {}", e);
                    None
                }
            })
            .collect();

        // Save this batch if we have data
        if !successful_data.is_empty() {
            let batch_count = successful_data.len();
            append_data_to_csv(&csv_path, successful_data).await?;
            total_saved += batch_count;
            println!("✓ Saved batch {} ({} days). Total saved so far: {}",
                     batch_num + 1, batch_count, total_saved);
        } else {
            println!("⚠ No data found for batch {}", batch_num + 1);
        }
    }

    println!("✅ Download finished for {}. Total days saved: {}", pair, total_saved);

    // Final summary
    if total_saved > 0 {
        let final_df = LazyCsvReader::new(&csv_path)
            .finish()?
            .collect()?;

        let timestamp_stats = final_df
            .lazy()
            .select([
                col("timestamp").min().alias("min_timestamp"),
                col("timestamp").max().alias("max_timestamp"),
                col("timestamp").count().alias("total_trades"),
            ])
            .collect()?;

        println!("📊 Final CSV summary:");
        println!("   File: {}", csv_path.display());
        println!("   Total trades: {}", timestamp_stats.column("total_trades")?.get(0)?);

        // Convert timestamps to readable dates
        let min_ts = timestamp_stats.column("min_timestamp")?.get(0)?.try_extract::<i64>()?;
        let max_ts = timestamp_stats.column("max_timestamp")?.get(0)?.try_extract::<i64>()?;

        let min_date = chrono::DateTime::from_timestamp_millis(
            if min_ts > 1_000_000_000_000_000 { min_ts / 1000 } else { min_ts }
        ).unwrap().date_naive();

        let max_date = chrono::DateTime::from_timestamp_millis(
            if max_ts > 1_000_000_000_000_000 { max_ts / 1000 } else { max_ts }
        ).unwrap().date_naive();

        println!("   Date range: {} to {}", min_date, max_date);
    }

    Ok(())
}

/// Downloads the data for a single day and returns it as a DataFrame.
/// Includes retry logic for transient failures.
async fn download_day_data(
    client: Arc<Client>,
    pair: &str,
    date: NaiveDate,
) -> Result<Option<DataFrame>> {
    const MAX_RETRIES: usize = 3;
    const RETRY_DELAY_MS: u64 = 1000;

    for attempt in 1..=MAX_RETRIES {
        let date_str = date.format("%Y-%m-%d").to_string();
        let url = format!(
            "{}/{}/{}-aggTrades-{}.zip",
            BASE_URL, pair, pair, date_str
        );

        match download_day_attempt(&client, &url, &date_str).await {
            Ok(Some(df)) => return Ok(Some(df)),
            Ok(None) => return Ok(None), // No data available (404)
            Err(e) if attempt < MAX_RETRIES => {
                eprintln!("Attempt {} failed for {}: {}. Retrying...", attempt, date_str, e);
                tokio::time::sleep(tokio::time::Duration::from_millis(RETRY_DELAY_MS * attempt as u64)).await;
                continue;
            }
            Err(e) => return Err(e),
        }
    }

    unreachable!()
}

/// Single download attempt for a day
async fn download_day_attempt(
    client: &Client,
    url: &str,
    date_str: &str,
) -> Result<Option<DataFrame>> {
    // Make the HTTP request
    let response = client.get(url).send().await?;

    // Check if the data exists for this day (Binance returns 404 if not)
    if response.status() == reqwest::StatusCode::NOT_FOUND {
        return Ok(None);
    }

    // Ensure the request was successful
    let response = response.error_for_status()?;
    let zip_data = response.bytes().await?;

    // Unzip and parse the data directly (no need for spawn_blocking)
    let mut archive = ::zip::ZipArchive::new(std::io::Cursor::new(zip_data))?;

    if archive.is_empty() {
        return Err(anyhow::anyhow!("Empty zip file for {}", date_str));
    }

    let mut file_in_zip = archive.by_index(0)?; // There's only one file in the zip
    let mut csv_content = String::new();
    file_in_zip.read_to_string(&mut csv_content)?;

    // Parse CSV content with Polars
    let df = CsvReadOptions::default()
        .with_has_header(false)
        .into_reader_with_file_handle(std::io::Cursor::new(csv_content.as_bytes()))
        .finish()?
        .lazy()
        .with_columns([
            col("column_1").alias("agg_trade_id"),
            col("column_2").alias("price"),
            col("column_3").alias("quantity"),
            col("column_4").alias("first_trade_id"),
            col("column_5").alias("last_trade_id"),
            col("column_6").alias("timestamp"),
            col("column_7").alias("is_buyer_maker"),
            col("column_8").alias("is_best_match"),
        ])
        .select([
            col("agg_trade_id"),
            col("price"),
            col("quantity"),
            col("first_trade_id"),
            col("last_trade_id"),
            col("timestamp"),
            col("is_buyer_maker"),
            col("is_best_match"),
        ])
        .collect()?;

    println!("Successfully downloaded and parsed data for {} ({} rows)",
             date_str, df.height());

    Ok(Some(df))
}

/// Append DataFrames to the CSV file
async fn append_data_to_csv(csv_path: &Path, dataframes: Vec<DataFrame>) -> Result<()> {
    let csv_path = csv_path.to_path_buf();

    tokio::task::spawn_blocking(move || -> Result<()> {
        // Concatenate all DataFrames
        let combined_df = concat(
            dataframes.into_iter().map(|df| df.lazy()).collect::<Vec<_>>(),
            UnionArgs::default()
        )?
        .collect()?;

        if csv_path.exists() {
            // Read existing data and append new data
            let existing_df = LazyCsvReader::new(&csv_path)
                .finish()?
                .collect()?;

            let final_df = concat(
                vec![existing_df.lazy(), combined_df.lazy()],
                UnionArgs::default()
            )?;

            // Sort by timestamp to maintain chronological order
            let sorted_df = final_df
                .sort(["timestamp"], SortMultipleOptions::default())
                .collect()?;

            // Write back to CSV
            let mut file = File::create(&csv_path)?;
            CsvWriter::new(&mut file)
                .include_header(true)
                .finish(&mut sorted_df.clone())?;
        } else {
            // Create new CSV file with headers
            let sorted_df = combined_df
                .lazy()
                .sort(["timestamp"], SortMultipleOptions::default())
                .collect()?;

            let mut file = File::create(&csv_path)?;
            CsvWriter::new(&mut file)
                .include_header(true)
                .finish(&mut sorted_df.clone())?;
        }

        Ok(())
    }).await??;

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // --- Configuration ---
    let pair_to_download = "BTCUSDT";
    // Binance's BTC data starts on 2017-08-17
    let start_date = NaiveDate::from_ymd_opt(2017, 8, 17).unwrap();
    let output_dir = PathBuf::from("./crypto_data");

    download_agg_trades_for_pair(pair_to_download, start_date, &output_dir).await?;

    // You can add more pairs here
    // let pair_to_download_2 = "ETHUSDT";
    // let start_date_2 = NaiveDate::from_ymd_opt(2017, 8, 17).unwrap();
    // download_agg_trades_for_pair(pair_to_download_2, start_date_2, &output_dir).await?;

    Ok(())
}