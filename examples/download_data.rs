// src/main.rs

use anyhow::Result;
use chrono::{Duration, NaiveDate};
use futures::stream::{self, StreamExt};
use reqwest::Client;
use std::fs::{self, File};
use std::io::copy;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use zip::ZipArchive;

const BASE_URL: &str = "https://data.binance.vision/data/spot/daily/aggTrades";
const MAX_CONCURRENT_REQUESTS: usize = 20; // Number of parallel downloads

/// Main entry point to download all aggregate trades for a given crypto pair.
async fn download_agg_trades_for_pair(
    pair: &str,
    start_date: NaiveDate,
    output_dir: &Path,
) -> Result<()> {
    let end_date = chrono::Utc::now().date_naive(); // Download up to today
    let client = Arc::new(Client::new());
    let pair_output_dir = Arc::new(output_dir.join(pair));
    fs::create_dir_all(pair_output_dir.as_ref())?;

    println!(
        "Starting download for {} from {} to {}",
        pair, start_date, end_date
    );

    // Create a stream of dates to process
    let dates: Vec<NaiveDate> = start_date
        .iter_days()
        .take_while(|&d| d <= end_date)
        .collect();

    // Process the downloads concurrently
    stream::iter(dates)
        .for_each_concurrent(MAX_CONCURRENT_REQUESTS, |date| {
            let client = Arc::clone(&client);
            let pair_output_dir = Arc::clone(&pair_output_dir);
            async move {
                if let Err(e) = download_and_unzip_day(client, pair, date, &pair_output_dir).await {
                    eprintln!("Error processing date {}: {}", date, e);
                }
            }
        })
        .await;

    println!("Download finished for {}.", pair);
    Ok(())
}

/// Downloads and unzips the data for a single day.
async fn download_and_unzip_day(
    client: Arc<Client>,
    pair: &str,
    date: NaiveDate,
    output_dir: &Path,
) -> Result<()> {
    let date_str = date.format("%Y-%m-%d").to_string();
    let url = format!(
        "{}/{}/{}-aggTrades-{}.zip",
        BASE_URL, pair, pair, date_str
    );

    // Define the final CSV path
    let csv_filename = format!("{}.csv", date.format("%Y_%m_%d"));
    let final_csv_path = output_dir.join(&csv_filename);

    // Skip if the file already exists
    if final_csv_path.exists() {
        println!("Skipping {}: File already exists.", date_str);
        return Ok(());
    }

    // Make the HTTP request
    let response = client.get(&url).send().await?;

    // Check if the data exists for this day (Binance returns 404 if not)
    if response.status() == reqwest::StatusCode::NOT_FOUND {
        println!("No data available for {}", date_str);
        return Ok(());
    }

    // Ensure the request was successful
    let response = response.error_for_status()?;
    let zip_data = response.bytes().await?;

    // Unzip the data in a blocking-safe thread to avoid stalling the async runtime
    tokio::task::spawn_blocking(move || -> Result<()> {
        let mut archive = ZipArchive::new(std::io::Cursor::new(zip_data))?;

        if archive.is_empty() {
            return Ok(()); // Empty zip file, nothing to do
        }

        let mut file_in_zip = archive.by_index(0)?; // There's only one file in the zip
        let mut outfile = File::create(&final_csv_path)?;
        copy(&mut file_in_zip, &mut outfile)?;

        println!("Successfully downloaded and unzipped data for {}", date_str);
        Ok(())
    })
    .await??; // The double `??` propagates errors from both the task and the inner function

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // --- Configuration ---
    let pair_to_download = "BTCUSDT";
    // Binance's BTC data starts on 2017-08-17
    let start_date = NaiveDate::from_ymd_opt(2017, 8, 17).unwrap();
    let output_dir = PathBuf::from("./crypto_data");

    download_agg_trades_for_pair(pair_to_download, start_date, &output_dir).await?;

    // You can add more pairs here
    // let pair_to_download_2 = "ETHUSDT";
    // let start_date_2 = NaiveDate::from_ymd_opt(2017, 8, 17).unwrap();
    // download_agg_trades_for_pair(pair_to_download_2, start_date_2, &output_dir).await?;

    Ok(())
}