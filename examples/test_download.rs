// Test script to verify CSV reading functionality

use anyhow::Result;
use polars::prelude::*;
use std::path::PathBuf;

fn main() -> Result<()> {
    let test_dir = PathBuf::from("./test_crypto_data");
    let csv_path = test_dir.join("BTCUSDT_agg_trades.csv");

    if csv_path.exists() {
        println!("Reading existing CSV file: {}", csv_path.display());

        let df = LazyCsvReader::new(&csv_path)
            .finish()?
            .collect()?;

        println!("CSV file contains {} rows", df.height());
        println!("Columns: {:?}", df.get_column_names());

        if df.height() > 0 {
            println!("First few rows:");
            println!("{}", df.head(Some(5)));

            // Check timestamp range
            let timestamp_stats = df
                .lazy()
                .select([
                    col("timestamp").min().alias("min_timestamp"),
                    col("timestamp").max().alias("max_timestamp"),
                ])
                .collect()?;

            println!("Timestamp range:");
            println!("{}", timestamp_stats);
        }
    } else {
        println!("CSV file not found at: {}", csv_path.display());
        println!("Run the download_data example first to create the CSV file.");
    }

    Ok(())
}
