# Binance Data Download Refactor

This document describes the refactoring of the Binance aggregate trades data downloader to use a single CSV file with Polars and support for resumable downloads.

## Key Changes

### 1. Single CSV File Output
- **Before**: Downloaded data was saved as individual CSV files per day (e.g., `2024_01_01.csv`, `2024_01_02.csv`)
- **After**: All data is consolidated into a single CSV file per trading pair (e.g., `BTCUSDT_agg_trades.csv`)

### 2. Resumable Downloads
- **Before**: No resume capability - would re-download existing files or skip them
- **After**: Automatically detects the latest date in existing CSV and resumes from the next day

### 3. Polars Integration
- **Before**: Raw CSV file handling
- **After**: Uses Polars DataFrames for efficient data processing, concatenation, and sorting

### 4. Improved Data Structure
The CSV file contains the following columns (based on Binance aggTrades format):
- `agg_trade_id`: Aggregate trade ID
- `price`: Trade price
- `quantity`: Trade quantity  
- `first_trade_id`: First trade ID in the aggregate
- `last_trade_id`: Last trade ID in the aggregate
- `timestamp`: Trade timestamp (microseconds for 2025+ data, milliseconds for older data)
- `is_buyer_maker`: Whether the buyer was the maker
- `is_best_match`: Whether the trade was the best price match

## Usage

### Running the Download
```bash
cargo run --example download_data
```

### Configuration
Edit the `main()` function in `examples/download_data.rs` to configure:
- Trading pair (default: "BTCUSDT")
- Start date (default: 2017-08-17 for BTC)
- Output directory (default: "./crypto_data")

### Testing CSV Reading
```bash
cargo run --example test_download
```

## Technical Implementation

### Resume Logic
1. Check if CSV file exists at `{output_dir}/{pair}_agg_trades.csv`
2. If exists, read the maximum timestamp and convert to date
3. Resume downloading from the day after the latest date
4. If no existing file, start from the configured start date

### Data Processing Pipeline
1. Download daily ZIP files concurrently (max 20 concurrent requests)
2. Extract and parse CSV content from each ZIP file
3. Convert to Polars DataFrames with proper column names
4. Concatenate all new data
5. Merge with existing data (if any)
6. Sort by timestamp to maintain chronological order
7. Write back to single CSV file

### Error Handling
- Gracefully handles missing data (404 responses from Binance)
- Continues processing other dates if individual downloads fail
- Maintains data integrity by sorting and deduplicating

## Benefits

1. **Efficiency**: Single file is easier to work with for analysis
2. **Resumability**: Can restart downloads without losing progress
3. **Data Integrity**: Automatic sorting ensures chronological order
4. **Performance**: Polars provides fast data processing
5. **Scalability**: Concurrent downloads with configurable limits

## File Structure
```
crypto_data/
└── BTCUSDT_agg_trades.csv  # Single consolidated file
```

## Dependencies
The refactor uses the existing dependencies:
- `polars` (v0.49.1) with features: "lazy", "csv", "temporal"
- `tokio` for async runtime
- `reqwest` for HTTP requests
- `chrono` for date handling
- `zip` for extracting downloaded files

## Future Enhancements
- Support for multiple trading pairs in parallel
- Data compression options
- Incremental backup strategies
- Data validation and quality checks
