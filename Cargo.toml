[package]
name = "candle-bert-time-series"
version = "0.1.0"
edition = "2024"

[dependencies]
candle-core = {version="0.9.1"}
candle-nn = "0.9.1"
candle-transformers = "0.9.1"
anyhow = "1"
serde ={ version = "1", features = ["derive"] }
serde_json = "1"
tracing = "0.1"


polars = { version = "0.49.1", features = ["lazy", "csv", "temporal"] }
tokio = { version = "1", features = ["full"] }
reqwest = { version = "0.12", features = ["rustls-tls", "stream"] }
chrono = "0.4"
zip = "4.2.0"
futures = "0.3"